
#define DOCTEST_CONFIG_IMPLEMENT
#include "doctest.h"

#include "iec61850_client.h"


#include <conio.h>

#include <iostream>
#include <vector>


using namespace std;

IedConnection g_con = nullptr;
IedClientError g_error;

const char* MmsTypeNames[16] = {
    "MMS_ARRAY",
    "MMS_STRUCTURE",
    "MMS_BOOLEAN",
    "MMS_BIT_STRING",
    "MMS_INTEGER",
    "MMS_UNSIGNED",
    "MMS_FLOAT",
    "MMS_OCTET_STRING",
    "MMS_VISIBLE_STRING",
    "MMS_GENERALIZED_TIME",
    "MMS_BINARY_TIME",
    "MMS_BCD",
    "MMS_OBJ_ID",
    "MMS_STRING",
    "MMS_UTC_TIME",
    "MMS_DATA_ACCESS_ERROR"
};




const char* getMmsValueTypeName(int valueType)
{
    if(valueType < 0 || valueType >= sizeof(MmsTypeNames) / sizeof(char*))
        return "MMS_UNKNOWN_TYPE";
    else
        return MmsTypeNames[valueType];
}


const char* getMmsValueTypeName(MmsValue* value)
{
    return getMmsValueTypeName(MmsValue_getType(value));
}


const char* getMmsErrorString(MmsError error)
{
    switch (error) {
        /* generic error codes */
        case MMS_ERROR_NONE: return "No error";
        case MMS_ERROR_CONNECTION_REJECTED: return "Connection rejected";
        case MMS_ERROR_CONNECTION_LOST: return "Connection lost";
        case MMS_ERROR_SERVICE_TIMEOUT: return "Service timeout";
        case MMS_ERROR_PARSING_RESPONSE: return "Parsing response error";
        case MMS_ERROR_HARDWARE_FAULT: return "Hardware fault";
        case MMS_ERROR_CONCLUDE_REJECTED: return "Conclude rejected";
        case MMS_ERROR_INVALID_ARGUMENTS: return "Invalid arguments";
        case MMS_ERROR_OUTSTANDING_CALL_LIMIT: return "Outstanding call limit";
        case MMS_ERROR_OTHER: return "Other error";

        /* confirmed error PDU codes */
        case MMS_ERROR_VMDSTATE_OTHER: return "VMD state other";

        case MMS_ERROR_APPLICATION_REFERENCE_OTHER: return "Application reference other";

        case MMS_ERROR_DEFINITION_OTHER: return "Definition other";
        case MMS_ERROR_DEFINITION_INVALID_ADDRESS: return "Definition invalid address";
        case MMS_ERROR_DEFINITION_TYPE_UNSUPPORTED: return "Definition type unsupported";
        case MMS_ERROR_DEFINITION_TYPE_INCONSISTENT: return "Definition type inconsistent";
        case MMS_ERROR_DEFINITION_OBJECT_UNDEFINED: return "Definition object undefined";
        case MMS_ERROR_DEFINITION_OBJECT_EXISTS: return "Definition object exists";
        case MMS_ERROR_DEFINITION_OBJECT_ATTRIBUTE_INCONSISTENT: return "Definition object attribute inconsistent";

        case MMS_ERROR_RESOURCE_OTHER: return "Resource other";
        case MMS_ERROR_RESOURCE_CAPABILITY_UNAVAILABLE: return "Resource capability unavailable";

        case MMS_ERROR_SERVICE_OTHER: return "Service other";
        case MMS_ERROR_SERVICE_OBJECT_CONSTRAINT_CONFLICT: return "Service object constraint conflict";

        case MMS_ERROR_SERVICE_PREEMPT_OTHER: return "Service preempt other";

        case MMS_ERROR_TIME_RESOLUTION_OTHER: return "Time resolution other";

        case MMS_ERROR_ACCESS_OTHER: return "Access other";
        case MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT: return "Access object non-existent";
        case MMS_ERROR_ACCESS_OBJECT_ACCESS_UNSUPPORTED: return "Access object access unsupported";
        case MMS_ERROR_ACCESS_OBJECT_ACCESS_DENIED: return "Access object access denied";
        case MMS_ERROR_ACCESS_OBJECT_INVALIDATED: return "Access object invalidated";
        case MMS_ERROR_ACCESS_OBJECT_VALUE_INVALID: return "Access object value invalid";
        case MMS_ERROR_ACCESS_TEMPORARILY_UNAVAILABLE: return "Access temporarily unavailable";

        case MMS_ERROR_FILE_OTHER: return "File other";
        case MMS_ERROR_FILE_FILENAME_AMBIGUOUS: return "File filename ambiguous";
        case MMS_ERROR_FILE_FILE_BUSY: return "File busy";
        case MMS_ERROR_FILE_FILENAME_SYNTAX_ERROR: return "File filename syntax error";
        case MMS_ERROR_FILE_CONTENT_TYPE_INVALID: return "File content type invalid";
        case MMS_ERROR_FILE_POSITION_INVALID: return "File position invalid";
        case MMS_ERROR_FILE_FILE_ACCESS_DENIED: return "File access denied";
        case MMS_ERROR_FILE_FILE_NON_EXISTENT: return "File non-existent";
        case MMS_ERROR_FILE_DUPLICATE_FILENAME: return "File duplicate filename";
        case MMS_ERROR_FILE_INSUFFICIENT_SPACE_IN_FILESTORE: return "File insufficient space in filestore";

        /* reject codes */
        case MMS_ERROR_REJECT_OTHER: return "Reject other";
        case MMS_ERROR_REJECT_UNKNOWN_PDU_TYPE: return "Reject unknown PDU type";
        case MMS_ERROR_REJECT_INVALID_PDU: return "Reject invalid PDU";
        case MMS_ERROR_REJECT_UNRECOGNIZED_SERVICE: return "Reject unrecognized service";
        case MMS_ERROR_REJECT_UNRECOGNIZED_MODIFIER: return "Reject unrecognized modifier";
        case MMS_ERROR_REJECT_REQUEST_INVALID_ARGUMENT: return "Reject request invalid argument";

        default: return "Unknown MMS error";
    }
}

void readManyVars(IedConnection con)
{
	const char* domainId = "USO_MTR_4_0_32_16CTRL";

	static const vector<const char*> nameList = {
		"DiagGGIO5$ST$Ind1",
		"DiagGGIO5$ST$Ind2",
		"DiagGGIO5$ST$Ind1",
		"DiagGGIO5$ST$Ind2",
		"DiagGGIO5$ST$Ind2",
		"DiagGGIO5$ST$Ind1",
		"DiagGGIO5$ST$Ind2",
		"DiagGGIO5$ST$Ind1",
		"DiagGGIO5$ST$Ind2",
		"DiagGGIO5$ST$Ind2",
		"DiagGGIO5$ST$Ind1",
		"DiagGGIO5$ST$Ind2",
		"DiagGGIO5$ST$Ind1",
		"DiagGGIO5$ST$Ind2",
		"DiagGGIO5$ST$Ind2",
		"DiagGGIO5$ST$Ind1",
		"DiagGGIO5$ST$Ind2",
		"DiagGGIO5$ST$Ind1",
		"DiagGGIO5$ST$Ind2",
		"DiagGGIO5$ST$Ind2",
		"DiagGGIO5$ST$Ind1",
		"DiagGGIO5$ST$Ind2",
		"DiagGGIO5$ST$Ind1",
		"DiagGGIO5$ST$Ind2",
		"DiagGGIO5$ST$Ind2",
		"DiagGGIO5$ST$Ind1",
		"DiagGGIO5$ST$Ind2",
		"DiagGGIO5$ST$Ind1",
		"DiagGGIO5$ST$Ind2",
		"DiagGGIO5$ST$Ind2",
	};

	MmsConnection mmsCon = IedConnection_getMmsConnection(con);
    MmsError err;
    LinkedList vars = LinkedList_create();

	for (const char* var : nameList)
		LinkedList_add(vars, (void*)var);


	cout << "Read many vars" << endl;

    MmsValue* result = MmsConnection_readMultipleVariables(
        mmsCon, &err,
        domainId,  /* domainId — имя логического устройства */
        vars
    );

	cout << "Finished" << endl;

	/*
    if (err == MMS_ERROR_NONE && result != NULL) {
        int count = MmsValue_getArraySize(result);
        for (int i = 0; i < count; ++i) {
            MmsValue* v = MmsValue_getElement(result, i);
            if (v != NULL)
                MmsValue_print(v);
            else
                printf("[null]\n");
        }
        MmsValue_delete(result);
    } else {
        printf("MMS readMultiple failed (err = %d)\n", err);
    }
	*/

    LinkedList_destroyStatic(vars);
}

void sSrv6_writeConstant(IedConnection con)
{
	const char* objectReference = "TEMPLATECTRL/LLN0.LocSta.ctlModel";
	cout << objectReference << endl;
	// Read
	cout << "Read...\n";
	MmsValue* value = IedConnection_readObject(con, &g_error,
		objectReference, IEC61850_FC_CF);
		
	if (value == NULL || MmsValue_getType(value) == MMS_DATA_ACCESS_ERROR)
	{
		cout << "Error" << endl;
		return;
	}	
	cout << "Type:" << getMmsValueTypeName(value) << endl;
	cout << "Value:" << MmsValue_toInt32(value) << endl;
	MmsValue_delete(value);

	// Write
	cout << "Write...\n";
	value = MmsValue_newInteger(1);
	IedConnection_writeObject(con, &g_error, objectReference, IEC61850_FC_CF, value);
	if (g_error != IED_ERROR_OK)
	{
		cout << "Error" << endl;
		return;
	}
	MmsValue_delete(value);

	
	//MmsValue* value = MmsValue_newInteger(1);
	//IedConnection_writeObject(con, &g_error, objectReference, IEC61850_FC_CF, value);
	
}


void sSrvN1a(IedConnection con)
{
	cout << "sSrvN1a\n";
	//GetLogicalDeviceDirectory
	MmsError mmsError;
	LinkedList names;
	MmsConnection mmsCon = IedConnection_getMmsConnection(con);
	names = MmsConnection_getDomainVariableNames(mmsCon, &mmsError, "TEMPLATECTR_L");
	if (mmsError == MMS_ERROR_NONE)
	{
		LinkedList element = LinkedList_getNext(names);
		while (element != NULL)
		{
			cout << (char*)element->data << endl;
			element = LinkedList_getNext(element);
		}
	}
	else
	{
		cout << "MMS Error:" << mmsError << endl;
	}
	LinkedList_destroy(names);	
}

void sSrvN1ae(IedConnection con)
{
	cout << "sSrvN1ae\n";
	//GetDataSetDirectoryRequest		
	MmsError mmsError;
	LinkedList names;
	MmsConnection mmsCon = IedConnection_getMmsConnection(con);

	//MmsConnection_readNamedVariableListDirectory(MmsConnection self, MmsError* mmsError,
    //    const char* domainId, const char* listName, bool* deletable);
	names = MmsConnection_readNamedVariableListDirectory(mmsCon, &mmsError, "TEMPLATECTR_L", "LLN0$XYZ", NULL);				
	cout << "MMS Error:" << getMmsErrorString(mmsError) << endl;	
	LinkedList_destroy(names);	
}

void sSrv6(IedConnection con)
{	
	cout << "sSrv6\n";
	sSrv6_writeConstant(con);
}



void readNonExistant(IedConnection con)
{
	cout << "Read non existant" << endl;

	const char* objectReference = "TEMPLATECTRL/LLN0.NamPlt.vendorr";
	cout << objectReference << endl;
	// Read
	cout << "Read...\n";
	MmsValue* value = IedConnection_readObject(con, &g_error,
		objectReference, IEC61850_FC_DC);
	if (value == NULL || MmsValue_getType(value) == MMS_DATA_ACCESS_ERROR)
	{
		cout << "Error" << endl;
		return;
	}
	cout << "Type:" << getMmsValueTypeName(value) << endl;
	cout << "Value:" << MmsValue_toString(value) << endl;
	MmsValue_delete(value);
}

void sSrvN1f(IedConnection con)
{
	cout << "sSrvN1f\n";
	readNonExistant(con);
}


void alternateAccess(IedConnection con)
{
	cout << "Alternate access" << endl;

	MmsValue* value;
	MmsError mmsError;
	MmsConnection mmsCon = IedConnection_getMmsConnection(con);

	
	value = MmsConnection_readVariableComponent(mmsCon, &mmsError, 
		"TEMPLATECTRL", "LLN0$DC", "NamPlt");
	cout << "MMS Error:" << getMmsErrorString(mmsError) << endl;
	if (value != NULL)
	{	
		MmsValue_delete(value);
	}

	value = MmsConnection_readVariableComponent(mmsCon, &mmsError, 
		"TEMPLATECTRL", "LLN0", "DC$NamPlt$vendor"); 
	cout << "MMS Error:" << getMmsErrorString(mmsError) << endl;
	if (value != NULL)
	{	
		MmsType valType = MmsValue_getType(value);
		cout << "Type:" << getMmsValueTypeName(valType) << endl;

		if (valType == MMS_VISIBLE_STRING)
		{
			cout << "Value:" << MmsValue_toString(value) << endl;	
		}		
		MmsValue_delete(value);
	}
}

void sSrv8(IedConnection con)
{
	cout << "sSrv8\n";
	alternateAccess(con);
}


// int old_main()
// {	
// 	IedConnection con = IedConnection_create();
// 	cout << "Connecting " <<  HOST << "..." << endl;
// 	IedConnection_connect(con, &g_error, HOST, 102);	
// 	if (g_error != IED_ERROR_OK)
// 	{
// 		cout << "Failed to connect\n";
// 		return 1;
// 	}
// 	cout << "Connected" << endl;

// 	sSrvN1a(con);	
// 	sSrvN1ae(con);
// 	sSrvN1f(con);
// 	sSrv6(con);
// 	sSrv8(con);
		

// 	cout << "press any key to finish..." << endl;
// 	_getch();

// 	cout << "Disconnecting" << endl;
// 	IedConnection_destroy(con);	
// 	return 0;
// }

class MmsValWrap {
    MmsValue* value = nullptr;
public:
    // Конструкторы
    MmsValWrap() = default;
    explicit MmsValWrap(MmsValue* v) : value(v) {}
    
    // Удаляем копирование
    MmsValWrap(const MmsValWrap&) = delete;
    MmsValWrap& operator=(const MmsValWrap&) = delete;

    // Перегруженный оператор = для MmsValue*
    MmsValWrap& operator=(MmsValue* v) 
	{
		if (value != v) 
		{
			if (value) 
			{
				MmsValue_delete(value);
			}
			value = v;
		}
		return *this;
    }

    // Деструктор
    ~MmsValWrap() 
	{ 
        if (value)
		{
			MmsValue_delete(value); 		
		}
    }

	MmsType getType() { return MmsValue_getType(value);}

	bool isNull() const { return value == nullptr; }
	bool isNotNull() const { return value != nullptr; }

    // Методы доступа
    MmsValue* get() { return value; } 
	// Shorthand for get()
	MmsValue* operator*() const { return value; }	
};

TEST_CASE("sSrv6 write constant") 
{
	IedClientError err;
	const char* objRef = "TEMPLATECTRL/LLN0.LocSta.ctlModel";

	MmsValWrap value;
		
	value = IedConnection_readObject(g_con, &err,
		objRef, IEC61850_FC_CF);
	REQUIRE(value.isNotNull());

	REQUIRE(value.getType() == MMS_INTEGER);
	REQUIRE(MmsValue_toInt32(*value) == 0);
				
	value = MmsValue_newInteger(1);
	IedConnection_writeObject(g_con, &err, objRef, IEC61850_FC_CF, *value);
	REQUIRE(err == IED_ERROR_ACCESS_DENIED);	
}

TEST_CASE("sSrv6 write Quality") 
{
	IedClientError err;
	const char* objRef = "TEMPLATECTRL/LLN0.LocSta.q";
		
	MmsValWrap value;

	value = IedConnection_readObject(g_con, &err, objRef, IEC61850_FC_ST);
	REQUIRE(value.isNotNull());
	REQUIRE(MmsValue_getType(*value) == MMS_BIT_STRING);			
		
	value = MmsValue_newBitString(1);
	IedConnection_writeObject(g_con, &err, objRef, IEC61850_FC_ST, *value);
	REQUIRE(err == IED_ERROR_ACCESS_DENIED);	
}

TEST_CASE("sSrv8 alternate access") 
{
	 value;
	MmsError mmsError;
	MmsConnection mmsCon = IedConnection_getMmsConnection(g_con);

	SUBCASE("Simple")
	{

		value = MmsConnection_readVariableComponent(mmsCon, &mmsError, 
			"TEMPLATECTRL", "LLN0", "ST");
		REQUIRE(mmsError == MMS_ERROR_NONE);
		REQUIRE(value != NULL);
		if(value != NULL) 
		{
			MmsValue_delete(value);	
		}
	}
	SUBCASE("Complex")
	{
		value = MmsConnection_readVariableComponent(mmsCon, &mmsError, 
			"TEMPLATECTRL", "LLN0", "DC$NamPlt$vendor"); 
		REQUIRE(mmsError == MMS_ERROR_NONE);
		REQUIRE(value != NULL);
		REQUIRE(MmsValue_getType(value) == MMS_VISIBLE_STRING);

		string vendor = MmsValue_toString(value);
		REQUIRE(vendor == "MTRA");
		if(value != NULL) 
		{
			MmsValue_delete(value);	
		}
	}				
}

int main(int argc, char** argv) {

	const char* ip = "127.0.0.1";
	IedClientError connError;
    	
	
    // Обработка своих флагов
    for (int i = 1; i < argc; ++i) 
	{
        if (std::string(argv[i]) == "--ip" && i + 1 < argc) 
		{
            ip = argv[++i];
        }        
    }

	doctest::Context context;

    // Теперь doctest заберёт только свои флаги и игнорирует всё остальное
    context.applyCommandLine(argc, argv);

	if (context.shouldExit()) 
	{
		// Здесь просто выведется help и завершится программа
        return context.run(); 
    }


	g_con = IedConnection_create();
	cout << "Connecting " << ip << "..." << endl;
	IedConnection_connect(g_con, &connError, ip, 102);
	if (connError != IED_ERROR_OK)
	{
		cerr << "Failed to connect " << endl;
		return 2;
	}
	cout << "Connected" << endl;

	int result = context.run();

	IedConnection_destroy(g_con);	

    return result;
}
