
#define DOCTEST_CONFIG_IMPLEMENT
#include "doctest.h"

#include "iec61850_client.h"
#include "MmsValWrap.h"

#include <windows.h>

#include <iostream>

using namespace std;

// MMS Server Capabilities
constexpr uint8_t MMS_SERVICE_WRITE = 0x04;
constexpr uint8_t MMS_SERVICE_GET_NAMED_VARIABLE_LIST_ATTRIBUTES = 0x08;

const char* DEFAULT_IP = "127.0.0.1";
//const char* DEFAULT_IP = "***************";

IedConnection g_con = nullptr;


TEST_CASE("FLOAT32 constant support")
{
	//TEMPLATEMEAS/MMTR1.TotVAh.pulsQty [CF]
	const char* objRef = "TEMPLATEMEAS/MMTR1.TotVAh.pulsQty";
	IedClientError err;
	MmsValWrap value;
	value = IedConnection_readObject(g_con, &err, objRef, IEC61850_FC_CF);
	REQUIRE(err == IED_ERROR_OK);	
	REQUIRE(value.isNotNull());
	REQUIRE(value.getType() == MMS_FLOAT);	
	REQUIRE(MmsValue_toFloat(*value) == doctest::Approx(1.0f));
}


// Тестируем наличие сервисов Write и GetNamedVariableListAttributes
TEST_CASE("sAss4 services Write and GetNamedVariableListAttributes supported")
{
	MmsConnection mmsCon = IedConnection_getMmsConnection(g_con);

	MmsConnectionParameters params = 
		MmsConnection_getMmsConnectionParameters(mmsCon);

	uint8_t* services = params.servicesSupported;	
	CHECK((services[0] & MMS_SERVICE_WRITE) != 0);
	CHECK(
		(services[1] & MMS_SERVICE_GET_NAMED_VARIABLE_LIST_ATTRIBUTES) != 0);
}

// Тестируем ошибку при обращении к журналу (который не поддержиывается)
TEST_CASE("Journal correct error") 
{
	MmsError mmsError;
	MmsConnection mmsCon = IedConnection_getMmsConnection(g_con);
	LinkedList names = MmsConnection_getDomainJournals(
		mmsCon, &mmsError, "TEMPLATECTRL");
	if(names != NULL)
	{
		LinkedList_destroy(names);
	}
	REQUIRE(mmsError == MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);
}


// Тестируем чтение какого-нибудь значения, которое доставляется через
// DataSlice. TEMPLATECTRL/LLN0.Beh.stVal это накопитель, который всегда 1
TEST_CASE("Read DatSlice value (SYSNAK) [device]")
{	
	const char* objRef = "TEMPLATECTRL/LLN0.Beh.stVal";
	IedClientError err;
	
	MmsValWrap value;
			
	value = IedConnection_readObject(g_con, &err, objRef, IEC61850_FC_ST);
	REQUIRE(err == IED_ERROR_OK);	
	REQUIRE(value.isNotNull());
	REQUIRE(value.getType() == MMS_INTEGER);
	// Сейчас Beh.stVal всегда 1
	REQUIRE(MmsValue_toInt32(*value) == 1);
}

TEST_CASE("sDsN1ae GetLogicalDeviceDirectory") 
{	
	//GetDataSetDirectoryRequest		
	MmsError mmsError;
	LinkedList names;
	MmsConnection mmsCon = IedConnection_getMmsConnection(g_con);
	
	names = MmsConnection_readNamedVariableListDirectory(mmsCon, &mmsError, 
		"TEMPLATECTR_L", "LLN0$XYZ", NULL);					
	if(names != NULL)
	{
		LinkedList_destroy(names);
	}
	REQUIRE(mmsError == MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);
}

TEST_CASE("sSrvN1f read non existent")
{		
	// "TEMPLATECTRL/LLN0.NamPlt.vendorr";
	MmsValWrap value;
	MmsError mmsError;
	MmsConnection mmsCon = IedConnection_getMmsConnection(g_con);
		
	value = MmsConnection_readVariable(mmsCon, &mmsError, 
			"TEMPLATECTRL", "LLN0$DC$NamPlt$vendo_r");
	REQUIRE(mmsError == MMS_ERROR_NONE);
	REQUIRE(value.isNotNull());
	REQUIRE(value.getType() == MMS_DATA_ACCESS_ERROR);
	// object-non-existent 
	REQUIRE(MmsValue_getDataAccessError(*value) == 10);
}

TEST_CASE("sSrvN1a GetLogicalDeviceDirectory error code") 
{
	MmsError mmsError;
	LinkedList names;
	MmsConnection mmsCon = IedConnection_getMmsConnection(g_con);	
	names = MmsConnection_getDomainVariableNames(mmsCon, &mmsError, "TEMPLATECTR_L");
	if(names != NULL)
	{
		LinkedList_destroy(names);
	}
	REQUIRE(mmsError == MMS_ERROR_ACCESS_OBJECT_NON_EXISTENT);	
}

TEST_CASE("sSrv6 write constant") 
{
	IedClientError err;
	const char* objRef = "TEMPLATECTRL/LLN0.LocSta.ctlModel";

	MmsValWrap value;
		
	value = IedConnection_readObject(g_con, &err,
		objRef, IEC61850_FC_CF);
	REQUIRE(value.isNotNull());

	REQUIRE(value.getType() == MMS_INTEGER);
	REQUIRE(MmsValue_toInt32(*value) == 0);
				
	value = MmsValue_newInteger(1);
	IedConnection_writeObject(g_con, &err, objRef, IEC61850_FC_CF, *value);
	REQUIRE(err == IED_ERROR_ACCESS_DENIED);	
}

// Тест не работает с эмулятором на PC
TEST_CASE("sSrv6 write uint out of range setting [device]")
{
	//TEMPLATEMEAS/MMXU1.TotW.zeroDb
	const char* objRef = "TEMPLATEMEAS/MMXU1.TotW.zeroDb";
	IedClientError err;
	
	MmsValWrap value;
		
	// Считываем старое значение
	value = IedConnection_readObject(g_con, &err, objRef, IEC61850_FC_CF);
	REQUIRE(err == IED_ERROR_OK);	
	REQUIRE(value.isNotNull());
	REQUIRE(value.getType() == MMS_UNSIGNED);

	uint32_t oldVal = MmsValue_toUint32(*value);
	// TEMPLATEMEAS/MMXU1.TotW.zeroDb это уставка с границами 0/0
	REQUIRE(oldVal == 0);

	// Записываем его же
	value = MmsValue_newUnsignedFromUint32(oldVal);
	IedConnection_writeObject(g_con, &err, objRef, IEC61850_FC_CF, *value);
	REQUIRE(err == IED_ERROR_OK);
	
	// Новое значение с выходом за границы 
	uint32_t newVal = 1;

	// Записываем новое значение
	value = MmsValue_newUnsignedFromUint32(newVal);
	// Это проверка что значение создалось правильно 
	REQUIRE(MmsValue_toUint32(*value) == 1);


	IedConnection_writeObject(g_con, &err, objRef, IEC61850_FC_CF, *value);
	// Должна быть ошибка 
	REQUIRE(err == IED_ERROR_TYPE_INCONSISTENT);	
}


// Тест не работает с эмулятором на PC
TEST_CASE("sSrv6 write float setting [device]")
{
	//TEMPLATEMEAS/MMXU1.TotW.rangeC.max.f
	const char* objRef = "TEMPLATEMEAS/MMXU1.A.phsA.rangeC.max.f";
	IedClientError err;
	
	MmsValWrap value;
		
	// Считываем старое значение
	value = IedConnection_readObject(g_con, &err, objRef, IEC61850_FC_CF);
	REQUIRE(err == IED_ERROR_OK);	
	REQUIRE(value.isNotNull());
	REQUIRE(value.getType() == MMS_FLOAT);

	float oldVal = MmsValue_toFloat(*value);

	// Придумываем новое значение
	float newVal = (oldVal == doctest::Approx(0.0f) ? 1.0f : 0.0f);

	// Записываем новое значение
	value = MmsValue_newFloat(newVal);
	err = IED_ERROR_OK;
	IedConnection_writeObject(g_con, &err, objRef, IEC61850_FC_CF, *value);
	REQUIRE_MESSAGE(err == IED_ERROR_OK, "value = " << newVal);

	// Считываем новое значение
	value = IedConnection_readObject(g_con, &err, objRef, IEC61850_FC_CF);
	REQUIRE(err == IED_ERROR_OK);
	REQUIRE(value.isNotNull());
	REQUIRE(value.getType() == MMS_FLOAT);
	float newReadValue = MmsValue_toFloat(*value);
	CHECK(newReadValue == doctest::Approx(newVal));	

	if(newReadValue != doctest::Approx(newVal))
	{
		// Попробуем через паузу
		Sleep(1000);
		value = IedConnection_readObject(g_con, &err, objRef, IEC61850_FC_CF);
		REQUIRE(err == IED_ERROR_OK);
		REQUIRE(value.isNotNull());
		REQUIRE(value.getType() == MMS_FLOAT);		
		REQUIRE(MmsValue_toFloat(*value) == doctest::Approx(newVal));
	}
}

TEST_CASE("sSrvN4 write Quality") 
{
	IedClientError err;
	const char* objRef = "TEMPLATECTRL/LLN0.LocSta.q";
		
	MmsValWrap value;

	value = IedConnection_readObject(g_con, &err, objRef, IEC61850_FC_ST);
	REQUIRE(value.isNotNull());
	REQUIRE(MmsValue_getType(*value) == MMS_BIT_STRING);			
		
	value = MmsValue_newBitString(1);
	IedConnection_writeObject(g_con, &err, objRef, IEC61850_FC_ST, *value);
	REQUIRE(err == IED_ERROR_ACCESS_DENIED);	
}

TEST_CASE("sSrv8 alternate access") 
{
	MmsValWrap value;
	MmsError mmsError;
	MmsConnection mmsCon = IedConnection_getMmsConnection(g_con);

	SUBCASE("Simple")
	{

		value = MmsConnection_readVariableComponent(mmsCon, &mmsError, 
			"TEMPLATECTRL", "LLN0", "ST");
		REQUIRE(mmsError == MMS_ERROR_NONE);
		REQUIRE(value.isNotNull());		
	}
	SUBCASE("Complex")
	{
		value = MmsConnection_readVariableComponent(mmsCon, &mmsError, 
			"TEMPLATECTRL", "LLN0", "DC$NamPlt$vendor"); 
		REQUIRE(mmsError == MMS_ERROR_NONE);
		REQUIRE(value.isNotNull());
		REQUIRE(value.getType() == MMS_VISIBLE_STRING);

		string vendor = MmsValue_toString(*value);
		REQUIRE(vendor == "MTRA");		
	}				
}

int main(int argc, char** argv) 
{

	const char* ip = nullptr;
	IedClientError connError;
    	
	
    // Обработка своих флагов
    for (int i = 1; i < argc; ++i) 
	{
        if (std::string(argv[i]) == "--ip" && i + 1 < argc) 
		{
            ip = argv[++i];
        }        		
    }

	if (ip == nullptr)
	{
		ip = DEFAULT_IP;		
		cout << "Use --ip x.x.x.x to override the default IP " << DEFAULT_IP 
			<< endl;
	}


	doctest::Context context;

    // Теперь doctest заберёт только свои флаги и игнорирует всё остальное
    context.applyCommandLine(argc, argv);

	if (context.shouldExit())
	{
		// Здесь просто выведется help и завершится программа
        return context.run();
    }


	g_con = IedConnection_create();
	cout << "Connecting " << ip << "..." << endl;
	IedConnection_connect(g_con, &connError, ip, 102);
	if (connError != IED_ERROR_OK)
	{
		cerr << "Failed to connect " << endl;
		return 2;
	}
	cout << "Connected" << endl;

	context.setOption("no-path-filenames", true);

	//!!!
	//context.setOption("test-case", "sSrv6 write uint out of range setting [device]");
	//context.setOption("test-case", "sSrv6 write float setting [device]");	
	//context.setOption("test-case-exclude", "*[device]*");

	int result = context.run();
	
	IedConnection_destroy(g_con);	

    return result;
}
